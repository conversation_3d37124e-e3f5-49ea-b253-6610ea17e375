import React, { useEffect, useState } from "react";
import { useDispatch } from "react-redux";
import {
  Card,
  Form,
  Input,
  Button,
  Space,
  Typography,
  message,
  Select,
  Radio,
} from "antd";
import { SaveOutlined, UndoOutlined } from "@ant-design/icons";
import {
  createWhitelist,
  updateWhitelistItem,
  batchCreateWhitelist,
  batchUpdateStatus,
  batchUpdateType,
} from "../../../redux/whitelistPage/whitelistPageSlice";
import { useGlobalConstants } from "@/hooks/useGlobalConstants";

const { Title } = Typography;
const { Option } = Select;
const { TextArea } = Input;

function AddWhitelistComponent({
  editingRecord,
  onSave,
  onCancel,
  tabType = "normal",
  selectedRowKeys = [],
  tabKey,
  saveTabFormData,
  getTabFormData,
  clearTabFormData,
}) {
  const dispatch = useDispatch();
  const [form] = Form.useForm();
  const [batchForm] = Form.useForm();
  const [batchItems, setBatchItems] = useState("");
  const [loading, setLoading] = useState(false);

  // 获取全局枚举常量
  const {
    getSelectOptions,
    getSelectOptionsByKey,
    loading: enumLoading
  } = useGlobalConstants();

  // 根据tabType确定组件模式
  const isBatchAdd = tabType === "batchAdd";
  const isBatchEditStatus = tabType === "batchEditStatus";
  const isBatchEditType = tabType === "batchEditType";
  const isEdit =
    editingRecord && !isBatchAdd && !isBatchEditStatus && !isBatchEditType;

  // 智能表单初始化
  useEffect(() => {
    if (
      editingRecord &&
      !isBatchAdd &&
      !isBatchEditStatus &&
      !isBatchEditType
    ) {
      // 编辑模式：设置表单值
      form.setFieldsValue({
        itemValue: editingRecord.内容,
        status: editingRecord.状态 === "启用" ? "ENABLED" : "DISABLED",
        language: editingRecord.语言 === "无" ? undefined : editingRecord.语言,
        itemType:
          editingRecord.类型 === "IP"
            ? "1"
            : editingRecord.类型 === "域名"
              ? "2"
              : "3",
      });
    } else if (!isBatchAdd && !isBatchEditStatus && !isBatchEditType) {
      // 普通添加模式：尝试恢复之前保存的表单数据
      if (tabKey && getTabFormData) {
        const savedFormData = getTabFormData(tabKey);
        if (savedFormData) {
          form.setFieldsValue(savedFormData);
        } else {
          form.resetFields();
        }
      } else {
        form.resetFields();
      }
    } else {
      // 批量操作模式：重置表单
      form.resetFields();
    }
  }, [editingRecord, form, isBatchAdd, isBatchEditStatus, isBatchEditType, tabKey, getTabFormData]);

  // 普通添加/编辑表单提交
  const handleFormSubmit = async () => {
    try {
      setLoading(true);
      const values = await form.validateFields();

      // 转换状态值为后端期望的格式
      if (values.status) {
        values.status = values.status === "ENABLED" ? "1" : "0";
      }

      if (isEdit) {
        // 更新白名单
        await dispatch(
          updateWhitelistItem({
            id: editingRecord.id,
            data: values,
          })
        ).unwrap();
        message.success("更新成功");
      } else {
        // 添加白名单
        await dispatch(createWhitelist(values)).unwrap();
        message.success("添加成功");
        form.resetFields();
        // 清除保存的表单数据
        if (tabKey && clearTabFormData) {
          clearTabFormData(tabKey);
        }
      }

      onSave && onSave();
    } catch (error) {
      console.error("操作失败:", error);
      message.error(isEdit ? "更新失败" : "添加失败");
    } finally {
      setLoading(false);
    }
  };

  // 批量添加表单提交
  const handleBatchAddSubmit = async () => {
    try {
      const values = await batchForm.validateFields();

      // 解析批量内容
      const items = batchItems
        .split("\n")
        .filter((item) => item.trim())
        .map((item) => ({
          itemValue: item.trim(),
          status: values.status,
          language: values.language,
          itemType: values.itemType,
        }));

      if (items.length === 0) {
        message.warning("请输入要添加的内容");
        return;
      }

      await dispatch(batchCreateWhitelist(items)).unwrap();
      message.success(`成功添加${items.length}条记录`);
      batchForm.resetFields();
      setBatchItems("");
      onSave && onSave();
    } catch (error) {
      console.error("批量添加失败:", error);
      message.error("批量添加失败");
    }
  };

  // 批量修改状态提交
  const handleBatchStatusSubmit = async () => {
    try {
      const values = await batchForm.validateFields();

      await dispatch(
        batchUpdateStatus({
          ids: selectedRowKeys,
          status: values.status,
        })
      ).unwrap();

      onSave && onSave();
    } catch (error) {
      console.error("批量修改状态失败:", error);
      message.error("批量修改状态失败");
    }
  };

  // 批量修改类型提交
  const handleBatchTypeSubmit = async () => {
    try {
      const values = await batchForm.validateFields();

      await dispatch(
        batchUpdateType({
          ids: selectedRowKeys,
          itemType: values.itemType,
        })
      ).unwrap();

      onSave && onSave();
    } catch (error) {
      console.error("批量修改类型失败:", error);
      message.error("批量修改类型失败");
    }
  };

  // 处理取消操作
  const handleCancel = () => {
    form.resetFields();
    batchForm.resetFields();
    setBatchItems("");
    if (tabKey && clearTabFormData) {
      clearTabFormData(tabKey);
    }
    onCancel && onCancel();
  };

  // 获取标题
  const getTitle = () => {
    if (isBatchAdd) return "批量添加白名单";
    if (isBatchEditStatus) return "批量修改状态";
    if (isBatchEditType) return "批量修改类型";
    return isEdit ? "编辑白名单" : "添加白名单";
  };

  // 获取提交处理函数
  const getSubmitHandler = () => {
    if (isBatchAdd) return handleBatchAddSubmit;
    if (isBatchEditStatus) return handleBatchStatusSubmit;
    if (isBatchEditType) return handleBatchTypeSubmit;
    return handleFormSubmit;
  };

  return (
    <div>
      <Title level={3}>{getTitle()}</Title>

      {/* 批量操作模式 */}
      {isBatchAdd || isBatchEditStatus || isBatchEditType ? (
        <Card>
          <Form form={batchForm} layout="vertical" style={{ maxWidth: 600 }}>
            {isBatchAdd && (
              <>
                <Form.Item
                  label="批量内容"
                  help="每行一个内容，支持IP、域名或文本"
                >
                  <TextArea
                    rows={8}
                    value={batchItems}
                    onChange={(e) => setBatchItems(e.target.value)}
                    placeholder="请输入要添加的内容，每行一个&#10;例如：&#10;***********&#10;example.com&#10;关键词"
                  />
                </Form.Item>
              </>
            )}

            {(isBatchAdd || isBatchEditStatus) && (
              <Form.Item
                name="status"
                label="状态"
                rules={[{ required: true, message: "请选择状态" }]}
                initialValue="ENABLED"
              >
                <Radio.Group
                  options={getSelectOptions('Whitelist.Status').map(option => ({
                    label: option.name,  // 显示使用 name
                    value: option.key    // 传值使用 key
                  }))}
                  loading={enumLoading}
                />
              </Form.Item>
            )}

            {(isBatchAdd || isBatchEditType) && (
              <Form.Item
                name="itemType"
                label="类型"
                rules={[{ required: true, message: "请选择类型" }]}
                initialValue="1"
              >
                <Select style={{ width: 200 }}>
                  <Option value="1">IP</Option>
                  <Option value="2">域名</Option>
                  <Option value="3">文本</Option>
                </Select>
              </Form.Item>
            )}

            {isBatchAdd && (
              <Form.Item name="language" label="语言">
                <Select
                  style={{ width: 200 }}
                  allowClear
                  placeholder="请选择语言"
                  options={getSelectOptionsByKey('Locale.Language')
                    .filter(option => ['zh_CN', 'en_US', 'fr_FR', 'ru_RU', 'vi_VN', 'es_ES'].includes(option.value))
                  }
                  loading={enumLoading}
                  showSearch
                  filterOption={(input, option) =>
                    (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
                  }
                />
              </Form.Item>
            )}

            {(isBatchEditStatus || isBatchEditType) && (
              <div style={{ marginBottom: 16 }}>
                <strong>选中的记录数量：{selectedRowKeys.length}</strong>
              </div>
            )}

            <Form.Item>
              <Space>
                <Button
                  type="primary"
                  onClick={getSubmitHandler()}
                  icon={<SaveOutlined />}
                >
                  {isBatchAdd ? "批量添加" : "批量修改"}
                </Button>
                <Button onClick={handleCancel} icon={<UndoOutlined />}>
                  取消
                </Button>
              </Space>
            </Form.Item>
          </Form>
        </Card>
      ) : (
        /* 普通添加/编辑模式 */
        <Card>
          <Form
            form={form}
            layout="vertical"
            style={{ maxWidth: 600 }}
            onValuesChange={() => {
              // 实时保存表单数据（仅在普通添加模式下）
              if (!isEdit && tabKey && saveTabFormData) {
                const formData = form.getFieldsValue();
                const hasData = Object.values(formData).some(
                  (value) => value !== undefined && value !== null && value.toString().trim() !== ""
                );
                if (hasData) {
                  saveTabFormData(tabKey, formData);
                }
              }
            }}
          >
            <Form.Item
              name="itemValue"
              label="内容"
              rules={[
                { required: true, message: "请输入内容" },
                { max: 500, message: "内容长度不能超过500字符" },
              ]}
            >
              <Input placeholder="请输入IP地址、域名或文本内容" />
            </Form.Item>

            <Form.Item
              name="itemType"
              label="类型"
              rules={[{ required: true, message: "请选择类型" }]}
              initialValue="1"
            >
              <Select style={{ width: 200 }}>
                <Option value="1">IP</Option>
                <Option value="2">域名</Option>
                <Option value="3">文本</Option>
              </Select>
            </Form.Item>

            <Form.Item
              name="status"
              label="状态"
              rules={[{ required: true, message: "请选择状态" }]}
              initialValue="ENABLED"
            >
              <Radio.Group
                options={getSelectOptions('Whitelist.Status').map(option => ({
                  label: option.name,  // 显示使用 name
                  value: option.key    // 传值使用 key
                }))}
                loading={enumLoading}
              />
            </Form.Item>

            <Form.Item name="language" label="语言">
              <Select
                style={{ width: 200 }}
                allowClear
                placeholder="请选择语言"
                options={getSelectOptionsByKey('Locale.Language')
                  .filter(option => ['zh_CN', 'en_US', 'fr_FR', 'ru_RU', 'vi_VN', 'es_ES'].includes(option.value))
                }
                loading={enumLoading}
                showSearch
                filterOption={(input, option) =>
                  (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
                }
              />
            </Form.Item>

            <Form.Item>
              <Space>
                <Button
                  type="primary"
                  onClick={handleFormSubmit}
                  icon={<SaveOutlined />}
                  loading={loading}
                >
                  保存
                </Button>
                <Button
                  onClick={handleCancel}
                  icon={<UndoOutlined />}
                  disabled={loading}
                >
                  取消
                </Button>
              </Space>
            </Form.Item>
          </Form>
        </Card>
      )}
    </div>
  );
}

export default AddWhitelistComponent;
