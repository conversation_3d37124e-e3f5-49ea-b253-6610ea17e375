import React, { useState, useEffect } from "react";
import {
  Card,
  Table,
  Button,
  Space,
  Select,
  Input,
  DatePicker,
  Typography,
  Row,
  Col,
  message,
  Tag,
  Popconfirm,
  Modal,
  Alert,
} from "antd";
import { DeleteOutlined } from "@ant-design/icons";
import { useDispatch, useSelector } from "react-redux";
import dayjs from "dayjs";
import {
  fetchWhitelistList,
  removeWhitelist,
  batchRemoveWhitelist,
  setSearchParams,
  setSelectedRowKeys,
  batchUpdateStatus,
  batchUpdateType,
} from "../../../redux/whitelistPage/whitelistPageSlice";
import PageTabs from '@/components/PageTabs/PageTabs';
import AddWhitelistComponent from './AddWhitelistComponent';
import { usePageTabs } from '@/hooks/usePageTabs';
import { useGlobalConstants } from "@/hooks/useGlobalConstants";

const { Title } = Typography;
const { Option } = Select;
const { RangePicker } = DatePicker;

function WhitelistListPage() {
  const dispatch = useDispatch();

  // 从Redux获取状态
  const { list, searchParams, selectedRowKeys, deleteData, batchDeleteData } = useSelector(
    (state) => state.whitelist
  );
  const loading = list.loading;

  // 计算是否有任何删除操作正在进行，用于覆盖整个表格的加载状态
  const isDeleting = deleteData.loading || batchDeleteData.loading;

  // 批量删除Modal相关状态
  const [batchDeleteModalVisible, setBatchDeleteModalVisible] = useState(false);

  // 获取全局枚举常量
  const {
    getSelectOptions,
    getSelectOptionsByKey,
    getEnumName,
    getEnumKey,
    loading: enumLoading
  } = useGlobalConstants();

  // 使用通用的标签管理 Hook
  const {
    activeTab,
    editingRecord,
    tabPanes,
    createTab,
    handleAdd,
    handleEdit,
    handleTabChange,
    handleTabEdit,
    handleSaveSuccess,
    handleCancel,
    isListTab,
    isTabType,
    saveTabFormData,
    getTabFormData,
    clearTabFormData,
  } = usePageTabs({
    listTabLabel: '白名单',
    tabTypes: {
      add: {
        label: '添加白名单',
        prefix: 'add'
      },
      edit: {
        label: '编辑',
        prefix: 'edit',
        getLabelFn: (record) => `编辑白名单 - ${record.内容}`
      },
      batchAdd: {
        label: '批量添加',
        prefix: 'batch-add'
      },
      batchEditStatus: {
        label: '批量修改状态',
        prefix: 'batch-edit-status'
      },
      batchEditType: {
        label: '批量修改类型',
        prefix: 'batch-edit-type'
      }
    },
    dataList: list.data,
    onSaveSuccess: () => {
      dispatch(fetchWhitelistList(searchParams));
    },
  });

  // 本地状态
  const [contentFilter, setContentFilter] = useState("");
  const [statusFilter, setStatusFilter] = useState("全部状态");
  const [languageFilter, setLanguageFilter] = useState("全部语言");
  const [typeFilter, setTypeFilter] = useState("全部类型");
  const [dateRange, setDateRange] = useState([null, null]);

  // 首次加载时获取数据
  useEffect(() => {
    dispatch(fetchWhitelistList({ page: 1, pageSize: 20 }));
  }, [dispatch]);

  // 获取状态标签颜色
  const getStatusColor = (status) => {
    switch (status) {
      case "启用":
        return "green";
      case "禁用":
        return "red";
      default:
        return "default";
    }
  };

  // 表格列定义
  const columns = [
    {
      title: "ID",
      dataIndex: "id",
      key: "id",
      width: 60,
      sorter: (a, b) => a.id - b.id,
    },
    {
      title: "状态",
      dataIndex: "状态",
      key: "状态",
      width: 100,
      render: (status) => <Tag color={getStatusColor(status)}>{status}</Tag>,
    },
    {
      title: "语言",
      dataIndex: "语言",
      key: "语言",
      width: 120,
      render: (language, record) => {
        // Locale.Language 特殊处理：显示 key 字段
        return getEnumName('Locale.Language', language) || language || "无";
      },
    },
    {
      title: "类型",
      dataIndex: "类型",
      key: "类型",
      width: 100,
    },
    {
      title: "内容",
      dataIndex: "内容",
      key: "内容",
      width: 300,
      ellipsis: true,
    },
    {
      title: "创建时间",
      dataIndex: "创建时间",
      key: "创建时间",
      width: 180,
      sorter: (a, b) => new Date(a.创建时间) - new Date(b.创建时间),
    },
    {
      title: "操作",
      key: "action",
      width: 150,
      fixed: "right",
      render: (_, record) => (
        <Space size="small">
          <Button
            size="small"
            onClick={() => handleEdit(record)}
            style={{
              backgroundColor: "#ff7a00",
              borderColor: "#ff7a00",
              color: "white",
            }}
          >
            编辑
          </Button>
          <Popconfirm
            title="确定删除该条记录吗?"
            onConfirm={() => handleDelete(record)}
            okText="确定"
            cancelText="取消"
          >
            <Button size="small" danger loading={deleteData.loading}>
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  // 行选择配置
  const rowSelection = {
    selectedRowKeys,
    onChange: (selectedKeys) => {
      dispatch(setSelectedRowKeys(selectedKeys));
    },
  };

  // 处理批量删除 - 打开Modal
  const handleBatchDelete = () => {
    if (selectedRowKeys.length === 0) {
      message.warning("请选择要删除的白名单");
      return;
    }
    setBatchDeleteModalVisible(true);
  };

  // 确认批量删除
  const handleBatchDeleteConfirm = async () => {
    try {
      await dispatch(batchRemoveWhitelist(selectedRowKeys)).unwrap();
      setBatchDeleteModalVisible(false);
    } catch (error) {
      // 错误已在slice中处理
    }
  };

  // 取消批量删除
  const handleBatchDeleteCancel = () => {
    setBatchDeleteModalVisible(false);
  };

  // 处理批量添加
  const handleBatchAdd = () => {
    return createTab('batchAdd');
  };

  // 处理批量修改状态
  const handleBatchModifyStatus = () => {
    if (selectedRowKeys.length === 0) {
      message.warning("请选择要修改状态的白名单");
      return;
    }
    return createTab('batchEditStatus');
  };

  // 处理批量修改类型
  const handleBatchModifyType = () => {
    if (selectedRowKeys.length === 0) {
      message.warning("请选择要修改类型的白名单");
      return;
    }
    return createTab('batchEditType');
  };

  // 处理删除
  const handleDelete = async (record) => {
    try {
      await dispatch(removeWhitelist(record.id)).unwrap();
    } catch (error) {
      // 错误已在slice中处理
    }
  };

  // 处理日期范围变更
  const handleDateRangeChange = (dates) => {
    setDateRange(dates);
  };

  // 处理查询
  const handleSearch = () => {
    const params = {
      itemValue: contentFilter,
      status:
        statusFilter === "全部状态" ? "" : statusFilter === "启用" ? "1" : "0",
      language: languageFilter === "全部语言" ? "" : languageFilter,
      itemType:
        typeFilter === "全部类型"
          ? ""
          : typeFilter === "IP"
            ? "1"
            : typeFilter === "域名"
              ? "2"
              : "3",
      createTimeStart: dateRange && dateRange[0] ? dateRange[0].unix() : null,
      createTimeEnd: dateRange && dateRange[1] ? dateRange[1].unix() : null,
      page: 1,
      pageSize: 20,
    };

    dispatch(setSearchParams(params));
    dispatch(fetchWhitelistList(params));
  };

  return (
    <>
      <Card style={{ backgroundColor: '#fff' }}>
        {/* 页面标签栏 */}
        <div className="page-tabs-wrapper">
          <PageTabs
            activeKey={activeTab}
            onChange={handleTabChange}
            onEdit={handleTabEdit}
            items={tabPanes}
            type="editable-card"
          />
        </div>

        {/* 条件渲染 */}
        {isListTab ? (
          <>
            {/* 筛选条件 */}
            <Card style={{ marginBottom: 16 }}>
              <Row gutter={[16, 16]} align="middle">
                <Col>
                  <span>状态</span>
                </Col>
                <Col>
                  <Select
                    value={statusFilter}
                    onChange={setStatusFilter}
                    style={{ width: 120 }}
                    options={[
                      { label: '全部状态', value: '全部状态' },
                      ...getSelectOptions('Whitelist.Status').map(option => ({
                        label: option.name,  // 显示使用 name
                        value: option.name   // 传值也使用 name，保持与现有逻辑一致
                      }))
                    ]}
                    loading={enumLoading}
                  />
                </Col>
                <Col>
                  <span>语言</span>
                </Col>
                <Col>
                  <Select
                    value={languageFilter}
                    onChange={setLanguageFilter}
                    style={{ width: 120 }}
                    options={[
                      { label: '全部语言', value: '全部语言' },
                      ...getSelectOptionsByKey('Locale.Language')
                    ]}
                    loading={enumLoading}
                    showSearch
                    filterOption={(input, option) =>
                      (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
                    }
                  />
                </Col>
                <Col>
                  <span>类型</span>
                </Col>
                <Col>
                  <Select
                    value={typeFilter}
                    onChange={setTypeFilter}
                    style={{ width: 120 }}
                    options={[
                      { label: '全部类型', value: '全部类型' },
                      ...getSelectOptions('Whitelist.ItemType').map(option => ({
                        label: option.name,  // 显示使用 name
                        value: option.name   // 传值也使用 name，保持与现有逻辑一致
                      }))
                    ]}
                    loading={enumLoading}
                  />
                </Col>
                <Col>
                  <Input
                    placeholder="搜索内容"
                    value={contentFilter}
                    onChange={(e) => setContentFilter(e.target.value)}
                    style={{ width: 150 }}
                  />
                </Col>
                <Col>
                  <span>创建时间</span>
                </Col>
                <Col>
                  <RangePicker
                    value={dateRange}
                    onChange={handleDateRangeChange}
                    style={{ width: 280 }}
                  />
                </Col>
                <Col>
                  <Button type="primary" onClick={handleSearch} loading={loading}>
                    查询
                  </Button>
                </Col>
              </Row>
            </Card>

            {/* 批量操作按钮 */}
            <Card style={{ marginBottom: 16 }}>
              <Space wrap>
                <Button
                  onClick={handleAdd}
                  style={{
                    backgroundColor: "#ff7a00",
                    borderColor: "#ff7a00",
                    color: "white",
                  }}
                >
                  添加
                </Button>
                <Button
                  danger
                  icon={<DeleteOutlined />}
                  disabled={selectedRowKeys.length === 0}
                  loading={batchDeleteData.loading}
                  onClick={handleBatchDelete}
                >
                  批量删除
                </Button>
                <Button
                  onClick={handleBatchAdd}
                  style={{
                    backgroundColor: "#52c41a",
                    borderColor: "#52c41a",
                    color: "white",
                  }}
                >
                  批量添加
                </Button>
                <Button
                  onClick={handleBatchModifyStatus}
                  disabled={selectedRowKeys.length === 0}
                  style={{
                    backgroundColor: "#1890ff",
                    borderColor: "#1890ff",
                    color: "white",
                  }}
                >
                  批量修改状态
                </Button>
                <Button
                  onClick={handleBatchModifyType}
                  disabled={selectedRowKeys.length === 0}
                  style={{
                    backgroundColor: "#f5222d",
                    borderColor: "#f5222d",
                    color: "white",
                  }}
                >
                  批量修改类型
                </Button>
              </Space>
            </Card>

            {/* 数据表格 */}
            <Card>
              <Table
                columns={columns}
                dataSource={list.data}
                loading={loading || isDeleting}
                rowSelection={rowSelection}
                rowKey="id"
                pagination={{
                  total: list.total,
                  current: list.curPage,
                  pageSize: 20,
                  showSizeChanger: true,
                  showQuickJumper: true,
                  showTotal: (total) => `总计 ${total} 条数据`,
                  onChange: (page, pageSize) => {
                    dispatch(
                      fetchWhitelistList({
                        ...searchParams,
                        page,
                        pageSize,
                      })
                    );
                  },
                  pageSizeOptions: ["10", "20", "50"],
                  size: "small",
                }}
                scroll={{ x: 1200 }}
                size="middle"
                bordered
              />
            </Card>
          </>
        ) : (
          /* 新的添加/编辑组件 */
          <AddWhitelistComponent
            editingRecord={activeTab.startsWith('add-') ? null : editingRecord}
            onSave={handleSaveSuccess}
            onCancel={handleCancel}
            tabType={isTabType('batchAdd') ? 'batchAdd' : isTabType('batchEditStatus') ? 'batchEditStatus' : isTabType('batchEditType') ? 'batchEditType' : 'normal'}
            selectedRowKeys={selectedRowKeys}
            tabKey={activeTab}
            saveTabFormData={saveTabFormData}
            getTabFormData={getTabFormData}
            clearTabFormData={clearTabFormData}
          />
        )}
      </Card>

      {/* 批量删除Modal */}
      <Modal
        title="批量删除白名单"
        open={batchDeleteModalVisible}
        onOk={handleBatchDeleteConfirm}
        onCancel={handleBatchDeleteCancel}
        okText="确定删除"
        cancelText="取消"
        width={500}
        okButtonProps={{ danger: true, loading: batchDeleteData.loading }}
        cancelButtonProps={{ disabled: batchDeleteData.loading }}
      >
        <div style={{ padding: '16px 0' }}>
          <Alert
            message={`已选中 ${selectedRowKeys.length} 个白名单项目`}
            type="info"
            showIcon
            style={{ marginBottom: 16 }}
          />

          <Alert
            message={`确定要删除选中的 ${selectedRowKeys.length} 个白名单项目吗？此操作不可撤销。`}
            type="warning"
            showIcon
          />
        </div>
      </Modal>
    </>
  );
}

export default WhitelistListPage;
